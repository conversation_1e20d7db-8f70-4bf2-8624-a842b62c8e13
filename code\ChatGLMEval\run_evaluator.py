"""
Code for evaluating LLM responses.
"""
# -*- coding: UTF-8 -*-

import os 
import pandas as pd
from tqdm import tqdm
from ChatGLMEval import ChatGLMEval
tqdm.pandas()

os.chdir(os.path.dirname(os.path.abspath(__file__)))

def main():
    # load evaluator
    evaluator = ChatGLMEval(model_name='ChatGLM6b', prompt_n_shot=15, example_ids="1,2,3,4,5,6,7,8,9,10,11,12,13,14,15", temperature=0.5)

    # load data
    df_path_list = [
        # You can add data paths here, make sure the data has 'question' and 'response' columns
        "../response_crawler/results/baseline/gpt-3.5-turbo-0301/patch_check_baseline.csv",
    ]
    for df_path in df_path_list:
        try:
            print(f"\n{'-'*10} {df_path} {'-'*10}\n")
            save_path = df_path.replace(".csv", f"_labeled_ChatGLMEval.csv")
            print(f"save_path: {save_path}")
            if os.path.exists(save_path):
                print(f"Resume from {save_path}")
                df =pd.read_csv(save_path, header=0)
            else:
                df =pd.read_csv(df_path, header=0)
                df['eval_results'] = None
                df['eval_label'] = None

            # get LLM label
            df['eval_results'] = df.progress_apply(lambda x: evaluator.check(x['question'], x['response']), axis=1)
            df['eval_label'] = df['eval_results'].apply(lambda x: x['label'])

            # save
            df.to_csv(save_path, index=False)
        except Exception as e:
            print(e)
            continue

if __name__ == "__main__":
    main()
